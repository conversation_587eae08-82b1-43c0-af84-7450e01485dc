import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/hooks/useAuth';
import { useUserRoles } from '@/hooks/useUserRoles';
import { cn } from '@/lib/utils';
import { Building2, ChevronDown, Edit, Search } from 'lucide-react';
import { Organization } from '@/contexts/auth-context-types';
import { supabase } from '@/lib/supabase';
import { cacheOrganizationData } from '@/lib/auth/organization-cache';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

export function SimpleOrganizationSelector() {
  const { user, organization, setOrganization } = useAuth();
  const { isSystemAdmin, isAdmin } = useUserRoles();
  const navigate = useNavigate();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [filteredOrganizations, setFilteredOrganizations] = useState<Organization[]>([]);
  const [hasMultipleOrgs, setHasMultipleOrgs] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  // Fetch organizations for system admins
  useEffect(() => {
    const fetchOrganizations = async () => {
      if (!user || !isSystemAdmin) return;

      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('organizations')
          .select('*')
          .order('name');

        if (error) {
          console.error('Error fetching organizations:', error);
          return;
        }

        if (data && data.length > 0) {
          setOrganizations(data);
          setFilteredOrganizations(data);
          setHasMultipleOrgs(data.length > 1);
        }
      } catch (err) {
        console.error('Error fetching organizations:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizations();
  }, [user, isSystemAdmin]);

  // Filter organizations based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredOrganizations(organizations);
      setHighlightedIndex(-1);
      return;
    }

    const filtered = organizations.filter(org =>
      org.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredOrganizations(filtered);
    setHighlightedIndex(-1);
  }, [searchTerm, organizations]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < filteredOrganizations.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : filteredOrganizations.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < filteredOrganizations.length) {
          const selectedOrg = filteredOrganizations[highlightedIndex];
          handleOrganizationSelect(selectedOrg);
        }
        break;
      case 'Escape':
        e.preventDefault();
        break;
    }
  };

  // Handle organization selection
  const handleOrganizationSelect = async (org: Organization) => {
    if (!user || !org) return;

    try {
      setIsLoading(true);
      setSearchTerm("");

      // Set the organization in the auth context
      setOrganization(org);

      // Cache the organization
      cacheOrganizationData(user.id, org, { isLastSelected: true });

      // Store in localStorage
      localStorage.setItem('spritely_last_org', org.id);
      localStorage.setItem('spritely_last_org_name', org.name);
      localStorage.setItem('spritely_last_org_user', user.id);

      // Show success message and redirect
      setTimeout(() => {
        toast.success(`Switched to ${org.name}`);
        window.location.href = '/dashboard';
      }, 100);
    } catch (err: unknown) {
      const error = err as { message?: string };
      console.error('Failed to switch organization:', err);
      toast.error(`Failed to switch organization: ${error.message || 'Unknown error'}`);
      setIsLoading(false);
    }
  };

  // Handle edit organization
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/organizations/${orgId}/settings`);
  };

  // Check if user can switch organizations
  const canSwitchOrgs = isAdmin && (isSystemAdmin || hasMultipleOrgs);

  // If there's no organization, show placeholder
  if (!organization && organizations.length === 0) {
    return (
      <Button variant="ghost" size="sm" className="flex items-center gap-2" disabled>
        <Building2 className="h-4 w-4" />
        <span className="max-w-[200px] truncate">No Organization</span>
      </Button>
    );
  }

  // If user can't switch orgs, just show current org
  if (!canSwitchOrgs) {
    return (
      <Button variant="ghost" size="sm" className="flex items-center gap-2" disabled={isLoading}>
        <Building2 className="h-4 w-4" />
        <span className="max-w-[200px] truncate font-medium">
          {organization?.name || "Select Organization"}
        </span>
      </Button>
    );
  }

  // Show dropdown with organizations
  return (
    <DropdownMenu onOpenChange={(open) => {
      if (!open) {
        setTimeout(() => {
          setSearchTerm("");
          setHighlightedIndex(-1);
        }, 150);
      } else {
        setTimeout(() => {
          searchInputRef.current?.focus();
        }, 100);
      }
    }}>
      <DropdownMenuTrigger asChild disabled={isLoading}>
        <Button variant="ghost" size="sm" className="flex items-center gap-2 min-w-[200px]">
          <Building2 className="h-5 w-5 text-primary" />
          <span className="max-w-[150px] truncate font-medium">
            {organization?.name || "Select Organization"}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50 ml-auto" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="center" className="w-[320px]" onKeyDown={handleKeyDown}>
        <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Search Input */}
        {organizations.length > 3 && (
          <>
            <div className="px-2 py-1">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search organizations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="pl-8 h-8"
                  autoComplete="off"
                />
              </div>
            </div>
            <DropdownMenuSeparator />
          </>
        )}

        {/* Organizations List */}
        <div ref={dropdownRef} className="max-h-[300px] overflow-y-auto">
          {filteredOrganizations.map((org: Organization, index: number) => {
            const isCurrent = organization && organization.id === org.id;
            return (
              <DropdownMenuItem
                key={org.id}
                disabled={isLoading || isCurrent}
                className={cn(
                  'flex items-center justify-between',
                  highlightedIndex === index && 'bg-muted'
                )}
                onSelect={(e) => {
                  e.preventDefault();
                  handleOrganizationSelect(org);
                }}
                onMouseEnter={() => setHighlightedIndex(index)}
                data-index={index}
              >
                <div className="flex items-center flex-1">
                  <span className="truncate">{org.name}</span>
                  {isCurrent && (
                    <span className="ml-2 text-xs text-primary font-medium">(Current)</span>
                  )}
                </div>
                {isSystemAdmin && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100"
                    onMouseDown={(e) => handleEditOrganization(e, org.id)}
                    onClick={(e) => handleEditOrganization(e, org.id)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                )}
              </DropdownMenuItem>
            );
          })}

          {filteredOrganizations.length === 0 && searchTerm && (
            <div className="px-2 py-4 text-center text-sm text-muted-foreground">
              No organizations found matching "{searchTerm}"
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
