import { useCallback, useEffect, useMemo, useState } from 'react';
import { OrganizationItem, SearchOptions } from '../types/organization-selector-types';
import { filterOrganizations, sortOrganizations } from '../utils/organization-utils';
import { debounce } from '../utils/keyboard-utils';

/**
 * Hook for managing organization search and filtering
 */
export function useOrganizationSearch(
  organizations: OrganizationItem[],
  searchOptions: SearchOptions = {},
  onSearchChange?: (searchTerm: string) => void
) {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce search term updates
  const debouncedSetSearchTerm = useMemo(
    () => debounce((term: string) => {
      setDebouncedSearchTerm(term);
      onSearchChange?.(term);
    }, 300),
    [onSearchChange]
  );

  // Update debounced search term when search term changes
  useEffect(() => {
    debouncedSetSearchTerm(searchTerm);
  }, [searchTerm, debouncedSetSearchTerm]);

  // Filter organizations based on search term
  const filteredOrganizations = useMemo(() => {
    const filtered = filterOrganizations(
      organizations,
      debouncedSearchTerm,
      searchOptions
    );

    // Sort filtered results
    return sortOrganizations(filtered, {
      sortBy: 'name',
      sortDirection: 'asc'
    });
  }, [organizations, debouncedSearchTerm, searchOptions]);

  // Update search term
  const updateSearchTerm = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setDebouncedSearchTerm('');
    onSearchChange?.('');
  }, [onSearchChange]);

  // Search statistics
  const searchStats = useMemo(() => {
    const hasSearch = debouncedSearchTerm.trim().length > 0;
    const totalCount = organizations.length;
    const filteredCount = filteredOrganizations.length;
    const hiddenCount = totalCount - filteredCount;

    return {
      hasSearch,
      totalCount,
      filteredCount,
      hiddenCount,
      hasResults: filteredCount > 0
    };
  }, [organizations.length, filteredOrganizations.length, debouncedSearchTerm]);

  // Highlight matching text in organization names
  const highlightMatch = useCallback((text: string, highlight: string): React.ReactNode => {
    if (!highlight.trim()) return text;

    const parts = text.split(new RegExp(`(${highlight})`, 'gi'));
    return parts.map((part, index) => 
      part.toLowerCase() === highlight.toLowerCase() ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  }, []);

  // Get search suggestions based on current organizations
  const getSearchSuggestions = useCallback((currentTerm: string): string[] => {
    if (!currentTerm.trim()) return [];

    const suggestions = new Set<string>();
    const term = currentTerm.toLowerCase();

    organizations.forEach(org => {
      const name = org.name.toLowerCase();
      
      // Add partial matches
      if (name.includes(term) && name !== term) {
        suggestions.add(org.name);
      }

      // Add word-based suggestions
      const words = name.split(/\s+/);
      words.forEach(word => {
        if (word.startsWith(term) && word !== term) {
          suggestions.add(word);
        }
      });
    });

    return Array.from(suggestions).slice(0, 5); // Limit to 5 suggestions
  }, [organizations]);

  // Handle search input events
  const handleSearchInput = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    updateSearchTerm(event.target.value);
  }, [updateSearchTerm]);

  const handleSearchKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow parent components to handle navigation keys
    if (['ArrowDown', 'ArrowUp', 'Enter', 'Escape'].includes(event.key)) {
      return; // Don't prevent default, let parent handle
    }
  }, []);

  const handleSearchClear = useCallback(() => {
    clearSearch();
  }, [clearSearch]);

  return {
    // State
    searchTerm,
    debouncedSearchTerm,
    filteredOrganizations,
    searchStats,

    // Actions
    updateSearchTerm,
    clearSearch,
    highlightMatch,
    getSearchSuggestions,

    // Event handlers
    handleSearchInput,
    handleSearchKeyDown,
    handleSearchClear,

    // Computed values
    hasSearch: searchStats.hasSearch,
    hasResults: searchStats.hasResults,
    isEmpty: searchStats.filteredCount === 0 && searchStats.hasSearch
  };
}
