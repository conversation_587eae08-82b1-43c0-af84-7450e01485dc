import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Building2, ChevronDown } from 'lucide-react';
import { OrganizationDropdown } from './components/OrganizationDropdown';
import { useOrganizationSelector } from './hooks/useOrganizationSelector';
import { 
  OrganizationSelectorProps,
  OrganizationSelectorConfig 
} from './types/organization-selector-types';
import { cn } from '@/lib/utils';

const defaultConfig: OrganizationSelectorConfig = {
  showSearch: true,
  searchThreshold: 3,
  maxHeight: 300,
  showActions: true,
  showCurrentIndicator: true,
  disableCurrentSelection: true,
  placeholder: 'Select Organization',
  searchPlaceholder: 'Search organizations...'
};

export function OrganizationSelector({
  config: userConfig,
  currentOrganization: providedCurrentOrg,
  organizations: providedOrganizations,
  disabled = false,
  className,
  trigger,
  loading: externalLoading = false,
  onSelect,
  onEdit,
  onManage,
  onCreate,
  onOpenChange,
  onError
}: OrganizationSelectorProps) {
  
  const config = { ...defaultConfig, ...userConfig };
  
  const {
    // State
    isOpen,
    isLoading,
    organizations,
    currentOrganization,
    error,
    
    // Computed values
    canSwitchOrganizations,
    canCreateOrganization,
    canManageOrganizations,
    hasMultipleOrganizations,
    isEmpty,
    
    // Actions
    setIsOpen,
    selectOrganization,
    clearError,
    
    // User info
    isSystemAdmin
  } = useOrganizationSelector(config, providedOrganizations);

  // Use provided current organization if available
  const effectiveCurrentOrg = providedCurrentOrg || currentOrganization;
  const effectiveLoading = externalLoading || isLoading;

  // Handle organization selection
  const handleSelect = async (org: any) => {
    try {
      if (onSelect) {
        onSelect(org);
      } else {
        await selectOrganization(org);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to select organization';
      onError?.(errorMessage);
    }
  };

  // Handle edit organization
  const handleEdit = (org: any) => {
    if (onEdit) {
      onEdit(org);
    } else {
      // Default edit behavior - navigate to organization settings
      window.location.href = `/organizations/${org.id}/settings`;
    }
  };

  // Handle manage organization
  const handleManage = (org: any) => {
    if (onManage) {
      onManage(org);
    } else {
      // Default manage behavior - navigate to organization management
      window.location.href = `/organizations/${org.id}/manage`;
    }
  };

  // Handle create organization
  const handleCreate = () => {
    if (onCreate) {
      onCreate();
    } else {
      // Default create behavior - navigate to create organization page
      window.location.href = '/organizations/create';
    }
  };

  // Handle manage all organizations
  const handleManageAll = () => {
    window.location.href = '/organizations';
  };

  // Handle view all organizations
  const handleViewAll = () => {
    window.location.href = '/organizations';
  };

  // Handle dropdown open/close
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    onOpenChange?.(open);
    
    if (!open) {
      clearError();
    }
  };

  // Show loading state
  if (effectiveLoading) {
    return (
      <Skeleton className="h-9 w-48" />
    );
  }

  // Show error state
  if (error) {
    return (
      <Button 
        variant="outline" 
        size="sm" 
        className="text-destructive border-destructive"
        onClick={clearError}
      >
        Error loading organizations
      </Button>
    );
  }

  // If no organizations and user can't create, show empty state
  if (isEmpty && !canCreateOrganization) {
    return (
      <Button variant="ghost" size="sm" disabled className="flex items-center gap-2">
        <Building2 className="h-4 w-4" />
        <span>No Organizations</span>
      </Button>
    );
  }

  // If user can't switch organizations, show current org only
  if (!canSwitchOrganizations && effectiveCurrentOrg) {
    return (
      <Button 
        variant="ghost" 
        size="sm" 
        disabled={disabled}
        className={cn('flex items-center gap-2', className)}
      >
        <Building2 className="h-4 w-4" />
        <span className="max-w-[200px] truncate font-medium">
          {effectiveCurrentOrg.name}
        </span>
      </Button>
    );
  }

  // Default trigger if none provided
  const defaultTrigger = (
    <Button 
      variant="ghost" 
      size="sm" 
      className={cn('flex items-center gap-2 min-w-[200px]', className)}
      disabled={disabled}
    >
      <Building2 className="h-5 w-5 text-primary" />
      <span className="max-w-[150px] truncate font-medium">
        {effectiveCurrentOrg?.name || config.placeholder}
      </span>
      <ChevronDown className="h-4 w-4 opacity-50 ml-auto" />
    </Button>
  );

  return (
    <OrganizationDropdown
      trigger={trigger || defaultTrigger}
      organizations={organizations}
      isOpen={isOpen}
      onOpenChange={handleOpenChange}
      onSelect={handleSelect}
      onEdit={handleEdit}
      onManage={handleManage}
      onCreate={handleCreate}
      onManageAll={handleManageAll}
      onViewAll={handleViewAll}
      searchPlaceholder={config.searchPlaceholder}
      showSearch={config.showSearch}
      searchThreshold={config.searchThreshold}
      showActions={config.showActions}
      canCreate={canCreateOrganization}
      canManageAll={canManageOrganizations}
      maxHeight={config.maxHeight}
      disabled={disabled}
    />
  );
}
