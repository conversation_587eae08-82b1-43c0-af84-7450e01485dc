import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/hooks/useAuth';
import { useUserRoles } from '@/hooks/useUserRoles';
import { cn } from '@/lib/utils';
import { Building2, ChevronDown } from 'lucide-react';
import { useCallback, useState } from 'react';
import {
    OrganizationSelectorConfig,
    OrganizationSelectorProps
} from './types/organization-selector-types';

const defaultConfig: OrganizationSelectorConfig = {
  showSearch: true,
  searchThreshold: 3,
  maxHeight: 300,
  showActions: true,
  showCurrentIndicator: true,
  disableCurrentSelection: true,
  placeholder: 'Select Organization',
  searchPlaceholder: 'Search organizations...'
};

export function OrganizationSelector({
  config: userConfig,
  currentOrganization: providedCurrentOrg,
  organizations: providedOrganizations,
  disabled = false,
  className,
  trigger,
  loading: externalLoading = false,
  onSelect,
  onEdit,
  onManage,
  onCreate,
  onOpenChange,
  onError
}: OrganizationSelectorProps) {

  const config = { ...defaultConfig, ...userConfig };
  const { user, organization: currentOrganization } = useAuth();
  const { isSystemAdmin, isAdmin } = useUserRoles();

  const [isOpen, setIsOpen] = useState(false);

  // Use provided current organization if available
  const effectiveCurrentOrg = providedCurrentOrg || currentOrganization;
  const effectiveLoading = externalLoading;

  // Simple permission checks
  const canSwitchOrganizations = isAdmin;
  const canCreateOrganization = isSystemAdmin;
  const canManageOrganizations = isSystemAdmin;

  // Handle dropdown open/close
  const handleOpenChange = useCallback((open: boolean) => {
    setIsOpen(open);
    onOpenChange?.(open);
  }, [onOpenChange]);

  // Handle organization selection
  const handleSelect = useCallback(async (org: any) => {
    try {
      if (onSelect) {
        onSelect(org);
      } else {
        // Default selection behavior - just show current org
        console.log('Selected organization:', org);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to select organization';
      onError?.(errorMessage);
    }
  }, [onSelect, onError]);

  // Show loading state
  if (effectiveLoading) {
    return (
      <Skeleton className="h-9 w-48" />
    );
  }

  // If user can't switch organizations, show current org only
  if (!canSwitchOrganizations && effectiveCurrentOrg) {
    return (
      <Button
        variant="ghost"
        size="sm"
        disabled={disabled}
        className={cn('flex items-center gap-2', className)}
      >
        <Building2 className="h-4 w-4" />
        <span className="max-w-[200px] truncate font-medium">
          {effectiveCurrentOrg.name}
        </span>
      </Button>
    );
  }

  // Default trigger if none provided
  const defaultTrigger = (
    <Button
      variant="ghost"
      size="sm"
      className={cn('flex items-center gap-2 min-w-[200px]', className)}
      disabled={disabled}
      onClick={() => setIsOpen(!isOpen)}
    >
      <Building2 className="h-5 w-5 text-primary" />
      <span className="max-w-[150px] truncate font-medium">
        {effectiveCurrentOrg?.name || config.placeholder}
      </span>
      <ChevronDown className="h-4 w-4 opacity-50 ml-auto" />
    </Button>
  );

  // For now, just return the trigger without the complex dropdown
  return trigger || defaultTrigger;
}
