import { Organization } from '@/contexts/auth-context-types';
import { supabase } from '@/lib/supabase';
import { Session, User } from '@supabase/supabase-js';
import {
    cacheOrganizationData,
    clearOrganizationCache as clearCache,
    getOrganizationFromCache
} from './organization-cache-exports';
import { EnhancedOrganization } from './organization-types';

// Import the cache key prefix for consistency
const ORG_CACHE_KEY_PREFIX = 'spritely_org_';

// Track in-flight requests to avoid duplicates
const pendingRequests: Record<string, Promise<Organization | null>> = {};

// Debounce timer for organization data fetch
const debounceTimers: Record<string, NodeJS.Timeout> = {};
const DEBOUNCE_DELAY = 200; // milliseconds - reduced to make UI more responsive

/**
 * Type-safe debounce function for asynchronous operations
 */
function debounce(
  fn: (user: User, session: Session) => Promise<Organization | null>,
  delay: number
): (user: User, session: Session) => Promise<Organization | null> {
  return (user: User, session: Session): Promise<Organization | null> => {
    // Create a unique key based on the function arguments
    const key = JSON.stringify([user.id, session]);

    // Check if there's already a pending request with this key
    if (pendingRequests[user.id]) {
      return pendingRequests[user.id];
    }

    return new Promise((resolve, reject) => {
      // Clear previous timer if it exists
      if (debounceTimers[key]) {
        clearTimeout(debounceTimers[key]);
      }

      // Set new timer
      debounceTimers[key] = setTimeout(async () => {
        try {
          const result = await fn(user, session);
          delete debounceTimers[key];
          resolve(result);
        } catch (error) {
          delete debounceTimers[key];
          reject(error);
        }
      }, delay);
    });
  };
}

/**
 * Fetch organization data for a user from the API
 * @param user The authenticated user
 * @param session The current session
 * @returns The user's organization data
 */
export async function fetchOrganizationData(
  user: User | null,
  session: Session | null
): Promise<Organization | null> {
  // Return null if user or session is not available
  if (!user || !session) {
    console.debug('fetchOrganizationData: No user or session, returning null');
    return null;
  }

  const userId = user.id;

  // Check if we already have a request in flight for this user
  const existingRequest = pendingRequests[userId];
  if (existingRequest) {
    console.debug('fetchOrganizationData: Reusing in-flight request for', { userId });
    return existingRequest;
  }

  // Check if we have a valid cached result
  const cachedData = getOrganizationFromCache(userId);
  if (cachedData?.organization) {
    console.debug('fetchOrganizationData: Returning cached organization', {
      organizationId: cachedData.organization.id,
      hasOrganization: cachedData.hasOrganization
    });
    // Return the cached organization and clear any pending requests
    delete pendingRequests[userId];
    return cachedData.organization;
  }

  console.debug('fetchOrganizationData: Fetching organization data for', { userId });

  // Create a new request and store it in pending requests
  const fetchPromise = debouncedFetchFromAPI(user, session)
    .then(organization => {
      // Cache the result (even if null)
      if (organization) {
        cacheOrganizationData(userId, organization);
      }

      // Clear from pending requests
      delete pendingRequests[userId];
      return organization;
    })
    .catch(error => {
      // Clear from pending requests on error
      delete pendingRequests[userId];
      console.error('fetchOrganizationData: Error fetching organization', { error, userId });
      throw error;
    });

  // Store the promise in pending requests
  pendingRequests[userId] = fetchPromise;

  return fetchPromise;
}

/**
 * Debounced version of the API call
 */
const debouncedFetchFromAPI = debounce(fetchOrganizationFromAPI, DEBOUNCE_DELAY);

/**
 * Clear organization cache for a specific user or all users
 * @param userId Optional user ID to clear cache for just that user
 */
export function clearOrganizationCache(userId?: string): void {
  // If a userId is specified, clear just that user's cache
  if (userId) {
    // Clear any pending requests for this user
    delete pendingRequests[userId];

    // Clear any debounce timers for this user
    Object.keys(debounceTimers).forEach(key => {
      if (key.includes(userId)) {
        clearTimeout(debounceTimers[key]);
        delete debounceTimers[key];
      }
    });

    // Clear the localStorage cache for this user
    clearCache(userId);
    console.debug('clearOrganizationCache: Cleared cache for user', { userId });
  } else {
    // Clear all pending requests
    Object.keys(pendingRequests).forEach(key => {
      delete pendingRequests[key];
    });

    // Clear all debounce timers
    Object.keys(debounceTimers).forEach(key => {
      clearTimeout(debounceTimers[key]);
      delete debounceTimers[key];
    });

    // Clear all localStorage caches
    clearCache();
    console.debug('clearOrganizationCache: Cleared all organization cache');
  }
}

/**
 * Force a refresh of organization data, bypassing cache
 * @param user The authenticated user
 * @param session The current session
 * @returns The user's organization data
 */
export async function forceRefreshOrganization(
  user: User | null,
  session: Session | null
): Promise<Organization | null> {
  if (!user || !session) {
    return null;
  }

  // Clear existing cache for this user
  clearOrganizationCache(user.id);

  // Fetch fresh data
  return fetchOrganizationData(user, session);
}

/**
 * Internal function to make the actual API call
 */
async function fetchOrganizationFromAPI(
  user: User,
  // Session parameter not used but kept for API consistency
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _session: Session
): Promise<Organization | null> {
  try {
    console.debug(`Fetching organizations for user: ${user.id} (${user.email})`);

    // Get all user roles for this user
    const { data: userRoles, error: userRolesError } = await supabase
      .from('user_roles')
      .select('organization_id, role, custom_permissions')
      .eq('user_id', user.id);

    if (userRolesError) {
      console.error('Error fetching user roles:', userRolesError.message);
      return null;
    }

    if (!userRoles || userRoles.length === 0) {
      console.warn('No user roles found for user:', user.id);
      return null;
    }

    console.debug(`Found ${userRoles.length} roles for user ${user.id}`);

    // Check if user is a system_admin with multi-org access
    // OR if the user has multiple organizations (regardless of role)
    const isMultiOrgAdmin = userRoles.some(role => {
      if ((role.role === 'system_admin' || role.role === 'org_admin') && role.custom_permissions) {
        // Need to check if custom_permissions is an object with multi_org property
        const permissions = role.custom_permissions as Record<string, unknown>;
        return permissions.multi_org === true;
      }
      return false;
    });

    // If the user has multiple organizations, they should be treated as multi-org
    const hasMultipleOrgs = new Set(userRoles.map(role => role.organization_id)).size > 1;

    // If user is a multi-org admin or has multiple organizations, we need to handle differently
    if (isMultiOrgAdmin || hasMultipleOrgs) {
      console.debug(`User has ${hasMultipleOrgs ? 'multiple organizations' : 'multi-org admin access'}, fetching all accessible organizations`);

      // Get all organization IDs this user has access to
      const orgIds = userRoles
        .filter(role => role.organization_id !== null && role.organization_id !== undefined)
        .map(role => role.organization_id as string); // Safe to cast after filtering

      if (orgIds.length === 0) {
        console.warn('Multi-org admin has no organization access');

        // For system admins without organizations, redirect to the organization selection page
        if (userRoles.some(role => role.role === 'system_admin')) {
          // Return a special flag to indicate this is a system admin without organizations
          return {
            id: 'system-admin-no-org',
            name: 'System Administrator',
            type: 'system',
            settings: {},
            isSystemAdmin: true,
            hasMultipleOrgs: false,
            availableOrgs: []
          } as unknown as Organization;
        }

        return null;
      }

      // Fetch all organizations this user has access to
      const { data: organizations, error: orgsError } = await supabase
        .from('organizations')
        .select('*')
        .in('id', orgIds);

      if (orgsError) {
        console.error('Error fetching organizations:', orgsError.message);
        return null;
      }

      if (!organizations || organizations.length === 0) {
        console.warn('No organizations found for user');
        return null;
      }

      // Check if we have a lastSelected organization in cache
      let primaryOrg = organizations[0];

      try {
        // Try to get the last selected organization from cache
        const cachedData = localStorage.getItem(`${ORG_CACHE_KEY_PREFIX}${user.id}`);
        if (cachedData) {
          const cache = JSON.parse(cachedData);
          if (cache.lastSelected) {
            // Find the organization in the available orgs
            const lastSelectedOrg = organizations.find(org => org.id === cache.lastSelected);
            if (lastSelectedOrg) {
              primaryOrg = lastSelectedOrg;
              console.info(`Using last selected organization: ${primaryOrg.name} (${primaryOrg.id})`);
            }
          }
        }
      } catch (e) {
        console.warn('Error retrieving last selected organization:', e);
      }

      // Add a flag to indicate this user has multiple organizations
      // Using the shared EnhancedOrganization type
      const enhancedOrg = primaryOrg as EnhancedOrganization;
      enhancedOrg.hasMultipleOrgs = true;
      enhancedOrg.availableOrgs = organizations;

      console.info(`Multi-org admin: Using ${primaryOrg.name} as primary organization (${organizations.length} total orgs)`);
      return primaryOrg;
    } else {
      // Regular user with a single organization
      // Get the first organization ID (most users will only have one)
      const organizationId = userRoles[0].organization_id;

      if (!organizationId) {
        console.warn('User role has no organization_id:', user.id);
        return null;
      }

      // Fetch the organization
      const { data: organization, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', organizationId)
        .single();

      if (orgError) {
        console.error('Error fetching organization:', orgError.message);
        return null;
      }

      if (!organization) {
        console.warn('Organization not found:', organizationId);
        return null;
      }

      console.info(`Successfully loaded organization: ${organization.id} (${organization.name})`);
      return organization;
    }
  } catch (error) {
    console.error('Exception fetching organization data:', error);
    throw error;
  }
}

/**
 * Prefetch organization data for a user
 * @returns A promise that resolves to the organization data
 */
export function prefetchOrganizationData(): Promise<Organization | null> {
  // Since fetchOrganizationData now requires user and session,
  // we should implement this differently or adjust the parameters
  console.warn('prefetchOrganizationData is deprecated - use fetchOrganizationData directly');
  return Promise.resolve(null);
}
