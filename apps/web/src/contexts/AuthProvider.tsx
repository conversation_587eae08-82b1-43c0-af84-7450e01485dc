import { signOut } from '@/lib/auth/auth-service';
import {
    authStateReducer,
    getOrganization,
    getUser
} from '@/lib/auth/auth-state-machine';
import { cacheOrganizationData } from '@/lib/auth/organization-cache';
import {
    clearOrganizationCache,
    fetchOrganizationData
} from '@/lib/auth/organization-service';
import { supabase } from '@/lib/supabase';
import type { AuthResponse, Session, UserResponse } from '@supabase/supabase-js';
import { useCallback, useEffect, useReducer, useRef, useState } from 'react';
import { AuthContext } from './auth-context';
import { AuthContextType, Organization } from './auth-context-types';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Use the state machine to manage auth state
  const [authState, dispatch] = useReducer(
    authStateReducer,
    { status: 'initializing' }
  );

  // Track session state
  const [session, setSession] = useState<Session | null>(null);

  // Track if component is mounted to avoid state updates after unmount
  const isMounted = useRef(true);

  // Track if we've already loaded organization data for the current user
  const loadedOrgForUser = useRef<string | null>(null);

  // Auth methods
  const handleSignUp = useCallback(async (
    email: string,
    password: string,
    metadata?: { first_name?: string; last_name?: string; organization_id?: string }
  ): Promise<AuthResponse> => {
    return supabase.auth.signUp({
      email,
      password,
      options: { data: metadata }
    });
  }, []);

  const handleSignIn = useCallback(async (
    email: string,
    password: string
  ): Promise<AuthResponse> => {
    return supabase.auth.signInWithPassword({
      email: email,
      password: password
    });
  }, []);

  const handleResetPassword = useCallback(async (
    email: string
  ): Promise<{ data: object | null; error: Error | null }> => {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email);
      return { data, error: error as Error | null };
    } catch (error) {
      return { data: null, error: error as Error };
    }
  }, []);

  const handleUpdatePassword = useCallback(async (
    newPassword: string
  ): Promise<UserResponse> => {
    return supabase.auth.updateUser({ password: newPassword });
  }, []);

  const handleSignOut = useCallback(async () => {
    const user = getUser(authState);
    if (user) {
      clearOrganizationCache(user.id);
    }
    await signOut();
  }, [authState]);

  const handleSetOrganization = useCallback((org: Organization | null) => {
    if (org) {
      dispatch({ type: 'LOAD_ORGANIZATION', organization: org });

      // Also cache the organization with the improved caching mechanism
      const user = getUser(authState);
      if (user) {
        try {
          // Cache the organization with isLastSelected=true
          cacheOrganizationData(user.id, org, { isLastSelected: true });

          // Also store in localStorage directly for redundancy
          try {
            localStorage.setItem('spritely_last_org', org.id);
            localStorage.setItem('spritely_last_org_name', org.name);
            localStorage.setItem('spritely_last_org_user', user.id);
          } catch (_e) {
            // Ignore localStorage errors
          }
        } catch (err) {
          window.console.warn('[AUTH] Failed to cache organization with improved caching:', err);
        }
      }
    } else {
      dispatch({ type: 'SIGN_OUT' });
    }
  }, [authState]);

  // Optimized organization data loading using the organization service
  const loadOrganizationData = useCallback(async () => {
    if (!session?.user) {
      // No user in session for loading organization
      return;
    }

    const userId = session.user.id;

    // Check if we already have an organization loaded
    const currentOrg = getOrganization(authState);
    if (currentOrg) {
      // Organization already loaded, no need to fetch again
      loadedOrgForUser.current = userId;
      return;
    }

    // Check if we've already loaded organization data for this user
    if (loadedOrgForUser.current === userId) {
      // Already attempted to load for this user, don't try again
      return;
    }

    try {
      loadedOrgForUser.current = userId;
      const organization = await fetchOrganizationData(
        session.user,
        session
      );

      if (isMounted.current) {
        if (organization) {
          dispatch({
            type: 'LOAD_ORGANIZATION',
            organization: organization
          });
        } else {
          // No organization found for user
        }
      }
    } catch (error) {
      window.console.error('[AUTH] Error loading organization data:', error);
      // Reset the flag on error so we can try again
      loadedOrgForUser.current = null;
      if (isMounted.current) {
        dispatch({
          type: 'ERROR',
          error: new Error('Failed to load organization data')
        });
      }
    }
  }, [session?.user?.id]);

  // Combined auth state and session management
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        const { data: { session: initialSession } } = await supabase.auth.getSession();

        if (mounted) {
          setSession(initialSession);

          if (initialSession?.user) {
            dispatch({ type: 'SIGN_IN', user: initialSession.user });
            loadOrganizationData();
          } else {
            dispatch({ type: 'SIGN_OUT' });
          }
        }
      } catch (error) {
        window.console.error('[AUTH] Error initializing auth:', error);
        if (mounted) {
          dispatch({ type: 'SIGN_OUT' });
        }
      }
    };

    initializeAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, newSession) => {
        if (!mounted) return;

        setSession(newSession);

        if (newSession?.user) {
          const currentUser = getUser(authState);
          const isNewUser = !currentUser || currentUser.id !== newSession.user.id;

          if (isNewUser) {
            if (currentUser) {
              clearOrganizationCache(currentUser.id);
            }
            // Reset the loaded org flag for new user
            loadedOrgForUser.current = null;
            dispatch({ type: 'SIGN_IN', user: newSession.user });
            // Load organization data for new user
            loadOrganizationData();
          } else if (!getOrganization(authState)) {
            // Only load organization data if we don't already have it
            loadOrganizationData();
          }
        } else {
          // Reset the loaded org flag on sign out
          loadedOrgForUser.current = null;
          dispatch({ type: 'SIGN_OUT' });
        }
      }
    );

    return () => {
      mounted = false;
      isMounted.current = false;
      subscription.unsubscribe();
    };
  }, [loadOrganizationData]);

  // Build the context value
  const contextValue: AuthContextType = {
    user: getUser(authState),
    session,
    organization: getOrganization(authState),
    isLoading: authState.status === 'initializing',
    hasOrganization: !!getOrganization(authState),
    signOut: handleSignOut,
    signIn: handleSignIn,
    signUp: handleSignUp,
    resetPassword: handleResetPassword,
    updatePassword: handleUpdatePassword,
    setOrganization: handleSetOrganization
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}