import { ErrorBoundary } from '@/components/ErrorBoundary';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LoadingStateManager } from '@/components/loading/LoadingStateManager';
import { NavigationManager } from '@/components/navigation/NavigationManager';
import { AuthLayout, DashboardLayout, MainLayout } from '@/layouts';
import {
    DashboardPage,
    ForgotPasswordPage,
    LoginPage,
    NotFoundPage,
    OrganizationSettingsPage,
    OrganizationsManagePage,
    OrganizationsPage,
    RegisterPage,
    ResetPasswordPage,
    SettingsPage,
    SetupRouter
} from '@/pages';
import { LandingPage } from '@/pages/home/<USER>';
import { Route, Routes } from 'react-router-dom';
import './App.css';

function App() {
  return (
    <ErrorBoundary>
      <LoadingStateManager>
        {/* NavigationManager needs direct access to routes to handle redirection */}
        <Routes>
          {/* Landing page route */}
          <Route path="/" element={<LandingPage />} />

          {/* Auth routes */}
          <Route element={<AuthLayout />}>
            <Route path="/login" element={
              <NavigationManager>
                <LoginPage />
              </NavigationManager>
            } />
            <Route path="/register" element={
              <NavigationManager>
                <RegisterPage />
              </NavigationManager>
            } />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />
          </Route>

          {/* Protected routes */}
          <Route element={<DashboardLayout />}>
            <Route
              path="/dashboard/*"
              element={
                <ErrorBoundary>
                  <NavigationManager>
                    <DashboardPage />
                  </NavigationManager>
                </ErrorBoundary>
              }
            />

            {/* Organization Management */}
            <Route
              path="/organizations"
              element={
                <ErrorBoundary>
                  <NavigationManager>
                    <OrganizationsPage />
                  </NavigationManager>
                </ErrorBoundary>
              }
            />

            <Route
              path="/organizations/manage"
              element={
                <ErrorBoundary>
                  <NavigationManager>
                    <OrganizationsManagePage />
                  </NavigationManager>
                </ErrorBoundary>
              }
            />

            <Route
              path="/organizations/:orgId/settings"
              element={
                <ErrorBoundary>
                  <NavigationManager>
                    <OrganizationSettingsPage />
                  </NavigationManager>
                </ErrorBoundary>
              }
            />

            <Route
              path="/settings"
              element={
                <ErrorBoundary>
                  <NavigationManager>
                    <SettingsPage />
                  </NavigationManager>
                </ErrorBoundary>
              }
            />
          </Route>

          {/* Other main app routes */}
          <Route element={<MainLayout />}>
            {/* Catch unauthorized access attempts */}
            <Route path="/unauthorized" element={<div>You are not authorized to access this page</div>} />
          </Route>

          {/* Setup route - only for users without organizations */}
          <Route
            path="/setup/*"
            element={
              <ProtectedRoute requireOrganization={false}>
                <ErrorBoundary>
                  <SetupRouter />
                </ErrorBoundary>
              </ProtectedRoute>
            }
          />

          {/* Handle 404 errors */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </LoadingStateManager>
    </ErrorBoundary>
  );
}

export default App;
